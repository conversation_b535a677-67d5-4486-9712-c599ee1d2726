import React, { useState, useEffect, useContext, useMemo } from "react";
import { Descriptions, Checkbox, Button, message, Upload, Modal, Table } from "antd";
import { EditOutlined, FileDoneOutlined } from "@ant-design/icons";
import { venderContext } from "../context/index";
import { updateVenderCompany, updateContactPersons, deleteContactPersons } from "@/service";
import { MyForm } from "@/components/MyForm";
import "./index.less";
import rest from "@urls/axios";

function Certification() {
  const { model, setUserInfo } = useContext(venderContext);
  const [companyData, setCompanyData] = useState(model.venderCompany);
  const [editType, setEditType] = useState(null);
  const [editState, setEditState] = useState(false);
  const [formOption, setFormOption] = useState(null);

  const formOptionObj = {
    baseInfoForm: [
      {
        type: "input",
        name: "name",
        attribute: {
          placeholder: "请填写公司名称",
          rule: {
            required: "公司名称不能为空",
            maxLength: {
              value: 20,
              message: "公司名称长度不能超过20个字符",
            },
          },
          label: { value: "公司名称", required: true },
        },
      },
      {
        type: "input",
        name: "corporate",
        attribute: {
          placeholder: "请填写法人姓名",
          rule: {
            required: "法人姓名不能为空",
            maxLength: {
              value: 20,
              message: "法人姓名长度不能超过20个字符",
            },
          },
          label: { value: "法人姓名", required: true },
        },
      },
      {
        type: "input",
        name: "corporateTel",
        attribute: {
          placeholder: "请填写法人联系方式",
          rule: {
            required: "法人联系方式不能为空",
            pattern: {
              value: /^(0|86|17951)?(1[0-9])[0-9]{9}$/,
              message: "请正确填写手机号",
            },
          },
          label: { value: "法人联系方式", required: true },
        },
      },
      {
        type: "input",
        name: "creditCode",
        attribute: {
          placeholder: "请填写统一社会信用代码",
          rule: {
            maxLength: {
              value: 20,
              message: "统一社会信用代码长度不能超过20个字符",
            },
          },
          label: { value: "统一社会信用代码" },
        },
      },
      {
        type: "input",
        name: "city",
        attribute: {
          placeholder: "请填写所在城市",
          rule: {
            required: "城市不能为空",
            maxLength: {
              value: 50,
              message: "城市长度不能超过50个字符",
            },
          },
          label: { value: "所在城市", required: true },
        },
      },
      {
        type: "input",
        name: "address",
        attribute: {
          placeholder: "请填写地址",
          rule: {
            required: "地址不能为空",
            maxLength: {
              value: 100,
              message: "地址长度不能超过100个字符",
            },
          },
          label: { value: "地址", required: true },
        },
      },
      {
        type: "input",
        name: "projectDockerName",
        attribute: {
          placeholder: "请填写项目对接人姓名",
          rule: {
            required: "项目对接人姓名不能为空",
            maxLength: {
              value: 20,
              message: "项目对接人姓名长度不能超过20个字符",
            },
          },
          label: { value: "项目对接人姓名", required: true },
        },
      },
      {
        type: "input",
        name: "projectDockerTel",
        attribute: {
          placeholder: "请填写项目对接人联系电话",
          rule: {
            required: "项目对接人联系电话不能为空",
            pattern: {
              value: /^(0|86|17951)?(1[0-9])[0-9]{9}$/,
              message: "请正确填写手机号",
            },
          },
          label: { value: "项目对接人联系电话", required: true },
        },
      },
      {
        type: "input",
        name: "projectDockerPosition",
        attribute: {
          placeholder: "请填写项目对接人职位",
          rule: {
            required: "项目对接人职位不能为空",
            maxLength: {
              value: 100,
              message: "项目对接人职位长度不能超过100个字符",
            },
          },
          label: { value: "项目对接人职位", required: true },
        },
      },
      {
        type: "checkbox",
        name: "venderType",
        attribute: {
          rule: {
            required: "请选择供应商类型",
          },
          label: { value: "供应商类型", required: true },
          options: [
            {
              label: "维修商",
              value: "maintainerT",
            },
            {
              label: "制造商",
              value: "manufacturerT",
            },
            {
              label: "供应商",
              value: "supplierT",
            },
          ],
        },
      },
    ],
    licenseForm: [
      {
        type: "input",
        name: "licenseCode",
        attribute: {
          placeholder: "请填写营业执照编码",
          rule: {
            required: "营业执照编码不能为空",
            maxLength: {
              value: 20,
              message: "营业执照编码长度不能超过20个字符",
            },
          },
          label: { value: "营业执照编码", required: true },
        },
      },
      {
        type: "DatePicker",
        name: "licenseStartDate",
        attribute: {
          rule: {
            required: "营业执照有效期不能为空",
          },
          allowClear: false,
          label: { value: "营业执照有效期-开始日期", required: true },
          format: "YYYY-MM-DD",
        },
      },
      {
        type: "DatePicker",
        name: "licenseEndDate",
        attribute: {
          rule: {
            required: "营业执照有效期不能为空",
          },
          allowClear: false,
          label: { value: "营业执照有效期-截止日期", required: true },
          format: "YYYY-MM-DD",
        },
      },
      {
        type: "input",
        name: "licenseScope",
        attribute: {
          placeholder: "请填写业务范围",
          rule: {
            maxLength: {
              value: 100,
              message: "业务范围长度不能超过100个字符",
            },
          },
          label: { value: "业务范围" },
        },
      },
      {
        type: "Upload",
        name: "licenseAttachments",
        attribute: {
          label: { value: "营业执照附件" },
          progress: { strokeWidth: 0.1, showInfo: false },
          showUploadList: true,
          maxCount: 1,
          customRequest: () => {},
          beforeUpload: file => {
            let fileType = file.name.substring(file.name.lastIndexOf(".") + 1);
            console.log("file====", file);
            const doesMatchFileType =
              ["jpg", "jpeg", "png", "pdf", "doc", "docx", "xls", "xlsx"].indexOf(fileType.toLowerCase()) > -1;
            if (!doesMatchFileType) {
              message.warning(
                `请上传 ${["jpg", "jpeg", "png", "pdf", "doc", "docx", "xls", "xlsx"].toString()} 格式的文件`,
                4,
              );
              return Upload.LIST_IGNORE;
            }
            const isLimitedSize = file.size / 1024 / 1024 < 50;
            if (!isLimitedSize) {
              message.warning("文件必须小于 50MB!");
              return Upload.LIST_IGNORE;
            }
          },
        },
      },
    ],
    /* +维修*/
    mQualificationForm: [
      {
        type: "Upload",
        name: "maintenanceQualificationAttachments",
        attribute: {
          label: { value: "维修资质相关的附件" },
          progress: { strokeWidth: 0.1, showInfo: false },
          showUploadList: true,
          customRequest: () => {},
          beforeUpload: file => {
            let fileType = file.name.substring(file.name.lastIndexOf(".") + 1);
            const doesMatchFileType =
              ["jpg", "jpeg", "png", "pdf", "doc", "docx", "xls", "xlsx"].indexOf(fileType.toLowerCase()) > -1;
            if (!doesMatchFileType) {
              message.warning(
                `请上传 ${["jpg", "jpeg", "png", "pdf", "doc", "docx", "xls", "xlsx"].toString()} 格式的文件`,
                4,
              );
              return Upload.LIST_IGNORE;
            }
            const isLimitedSize = file.size / 1024 / 1024 < 50;
            if (!isLimitedSize) {
              message.warning("文件必须小于 50MB!");
              return Upload.LIST_IGNORE;
            }
          },
        },
      },
    ],
    permitForm: [
      {
        type: "input",
        name: "permitCode",
        attribute: {
          placeholder: "请填写许可证编码",
          rule: {
            required: "许可证编码不能为空",
            maxLength: {
              value: 30,
              message: "许可证编码长度不能超过30个字符",
            },
          },
          label: { value: "许可证编码", required: true },
        },
      },
      {
        type: "DatePicker",
        name: "permitStartDate",
        attribute: {
          allowClear: true,
          label: {
            value: "许可证有效期-开始日期",
          },
          format: "YYYY-MM-DD",
        },
      },
      {
        type: "DatePicker",
        name: "permitEndDate",
        attribute: {
          allowClear: true,
          label: {
            value: "许可证有效期-截止日期",
          },
          format: "YYYY-MM-DD",
        },
      },
      {
        type: "Upload",
        name: "permitAttachments",
        attribute: {
          label: { value: "许可证附件" },
          progress: { strokeWidth: 0.1, showInfo: false },
          showUploadList: true,
          maxCount: 1,
          customRequest: () => {},
          beforeUpload: file => {
            let fileType = file.name.substring(file.name.lastIndexOf(".") + 1);
            console.log("file====", file);
            const doesMatchFileType =
              ["jpg", "jpeg", "png", "pdf", "doc", "docx", "xlsx", "xls"].indexOf(fileType.toLowerCase()) > -1;
            if (!doesMatchFileType) {
              message.warning(
                `请上传 ${["jpg", "jpeg", "png", "pdf", "doc", "docx", "xlsx", "xls"].toString()} 格式的文件`,
                4,
              );
              return Upload.LIST_IGNORE;
            }
            const isLimitedSize = file.size / 1024 / 1024 < 50;
            if (!isLimitedSize) {
              message.warning("文件必须小于 50MB!");
              return Upload.LIST_IGNORE;
            }
          },
        },
      },
      {
        type: "Upload",
        name: "afterSaleServiceAttachments",
        attribute: {
          label: { value: "售后服务承诺附件" },
          progress: { strokeWidth: 0.1, showInfo: false },
          showUploadList: true,
          maxCount: 1,
          customRequest: () => {},
          beforeUpload: file => {
            let fileType = file.name.substring(file.name.lastIndexOf(".") + 1);
            console.log("file====", file);
            const doesMatchFileType =
              ["jpg", "jpeg", "png", "pdf", "doc", "docx", "xlsx", "xls"].indexOf(fileType.toLowerCase()) > -1;
            if (!doesMatchFileType) {
              message.warning(
                `请上传 ${["jpg", "jpeg", "png", "pdf", "doc", "docx", "xlsx", "xls"].toString()} 格式的文件`,
                4,
              );
              return Upload.LIST_IGNORE;
            }
            const isLimitedSize = file.size / 1024 / 1024 < 50;
            if (!isLimitedSize) {
              message.warning("文件必须小于 50MB!");
              return Upload.LIST_IGNORE;
            }
          },
        },
      },
      {
        type: "Upload",
        name: "authorizationAttachments",
        attribute: {
          label: { value: "授权书附件" },
          progress: { strokeWidth: 0.1, showInfo: false },
          showUploadList: true,
          maxCount: 1,
          customRequest: () => {},
          beforeUpload: file => {
            let fileType = file.name.substring(file.name.lastIndexOf(".") + 1);
            console.log("file====", file);
            const doesMatchFileType =
              ["jpg", "jpeg", "png", "pdf", "doc", "docx", "xlsx", "xls"].indexOf(fileType.toLowerCase()) > -1;
            if (!doesMatchFileType) {
              message.warning(
                `请上传 ${["jpg", "jpeg", "png", "pdf", "doc", "docx", "xlsx", "xls"].toString()} 格式的文件`,
                4,
              );
              return Upload.LIST_IGNORE;
            }
            const isLimitedSize = file.size / 1024 / 1024 < 50;
            if (!isLimitedSize) {
              message.warning("文件必须小于 50MB!");
              return Upload.LIST_IGNORE;
            }
          },
        },
      },
    ],
    /* +备案*/
    businessFilingForm: [
      {
        type: "input",
        name: "businessFilingCode",
        attribute: {
          placeholder: "请填写备案凭证编码",
          rule: {
            maxLength: {
              value: 30,
              message: "备案凭证编码长度不能超过30个字符",
            },
          },
          label: { value: "备案凭证编码" },
        },
      },
      {
        type: "DatePicker",
        name: "businessFilingStartDate",
        attribute: {
          allowClear: true,
          label: {
            value: "备案凭证有效期-开始日期",
          },
          format: "YYYY-MM-DD",
        },
      },
      {
        type: "DatePicker",
        name: "businessFilingEndDate",
        attribute: {
          allowClear: true,
          label: {
            value: "备案凭证有效期-截止日期",
          },
          format: "YYYY-MM-DD",
        },
      },
      {
        type: "Upload",
        name: "businessFilingAttachments",
        attribute: {
          label: { value: "备案凭证附件" },
          progress: { strokeWidth: 0.1, showInfo: false },
          showUploadList: true,
          maxCount: 1,
          customRequest: () => {},
          beforeUpload: file => {
            let fileType = file.name.substring(file.name.lastIndexOf(".") + 1);
            console.log("file====", file);
            const doesMatchFileType =
              ["jpg", "jpeg", "png", "pdf", "doc", "docx", "xlsx", "xls"].indexOf(fileType.toLowerCase()) > -1;
            if (!doesMatchFileType) {
              message.warning(
                `请上传 ${["jpg", "jpeg", "png", "pdf", "doc", "docx", "xlsx", "xls"].toString()} 格式的文件`,
                4,
              );
              return Upload.LIST_IGNORE;
            }
            const isLimitedSize = file.size / 1024 / 1024 < 50;
            if (!isLimitedSize) {
              message.warning("文件必须小于 50MB!");
              return Upload.LIST_IGNORE;
            }
          },
        },
      },
      {
        type: "Upload",
        name: "businessFilingAfterSaleServiceAttachments",
        attribute: {
          label: { value: "售后服务承诺附件" },
          progress: { strokeWidth: 0.1, showInfo: false },
          showUploadList: true,
          maxCount: 1,
          customRequest: () => {},
          beforeUpload: file => {
            let fileType = file.name.substring(file.name.lastIndexOf(".") + 1);
            const doesMatchFileType =
              ["jpg", "jpeg", "png", "pdf", "doc", "docx", "xlsx", "xls"].indexOf(fileType.toLowerCase()) > -1;
            if (!doesMatchFileType) {
              message.warning(
                `请上传 ${["jpg", "jpeg", "png", "pdf", "doc", "docx", "xlsx", "xls"].toString()} 格式的文件`,
                4,
              );
              return Upload.LIST_IGNORE;
            }
            const isLimitedSize = file.size / 1024 / 1024 < 50;
            if (!isLimitedSize) {
              message.warning("文件必须小于 50MB!");
              return Upload.LIST_IGNORE;
            }
          },
        },
      },
      {
        type: "Upload",
        name: "businessFilingAuthorizationAttachments",
        attribute: {
          label: { value: "授权书附件" },
          progress: { strokeWidth: 0.1, showInfo: false },
          showUploadList: true,
          maxCount: 1,
          customRequest: () => {},
          beforeUpload: file => {
            let fileType = file.name.substring(file.name.lastIndexOf(".") + 1);
            const doesMatchFileType =
              ["jpg", "jpeg", "png", "pdf", "doc", "docx", "xlsx", "xls"].indexOf(fileType.toLowerCase()) > -1;
            if (!doesMatchFileType) {
              message.warning(
                `请上传 ${["jpg", "jpeg", "png", "pdf", "doc", "docx", "xlsx", "xls"].toString()} 格式的文件`,
                4,
              );
              return Upload.LIST_IGNORE;
            }
            const isLimitedSize = file.size / 1024 / 1024 < 50;
            if (!isLimitedSize) {
              message.warning("文件必须小于 50MB!");
              return Upload.LIST_IGNORE;
            }
          },
        },
      },
    ],
    manufacturingForm: [
      {
        type: "input",
        name: "manufacturingPermitCode",
        attribute: {
          placeholder: "请填写生产许可证编码",
          rule: {
            required: "生产许可证编码不能为空",
            maxLength: {
              value: 20,
              message: "生产许可证编码长度不能超过20个字符",
            },
          },
          label: { value: "生产许可证编码", required: true },
        },
      },
      {
        type: "DatePicker",
        name: "manufacturingPermitStartDate",
        attribute: {
          allowClear: true,
          label: {
            value: "生产许可证有效期-开始日期",
          },
          format: "YYYY-MM-DD",
        },
      },
      {
        type: "DatePicker",
        name: "manufacturingPermitEndDate",
        attribute: {
          allowClear: true,
          label: {
            value: "生产许可证有效期-截止日期",
          },
          format: "YYYY-MM-DD",
        },
      },
      {
        type: "Upload",
        name: "manufacturingPermitAttachments",
        attribute: {
          label: { value: "生产许可证附件" },
          progress: { strokeWidth: 0.1, showInfo: false },
          showUploadList: true,
          maxCount: 1,
          customRequest: () => {},
          beforeUpload: file => {
            let fileType = file.name.substring(file.name.lastIndexOf(".") + 1);
            console.log("file====", file);
            const doesMatchFileType =
              ["jpg", "jpeg", "png", "pdf", "doc", "docx", "xlsx", "xls"].indexOf(fileType.toLowerCase()) > -1;
            if (!doesMatchFileType) {
              message.warning(
                `请上传 ${["jpg", "jpeg", "png", "pdf", "doc", "docx", "xlsx", "xls"].toString()} 格式的文件`,
                4,
              );
              return Upload.LIST_IGNORE;
            }
            const isLimitedSize = file.size / 1024 / 1024 < 50;
            if (!isLimitedSize) {
              message.warning("文件必须小于 50MB!");
              return Upload.LIST_IGNORE;
            }
          },
        },
      },
    ],
    /*生产备案*/
    manufacturingFilingForm: [
      {
        type: "input",
        name: "manufacturingFilingCode",
        attribute: {
          placeholder: "请填写生产备案凭证编码",
          rule: {
            maxLength: {
              value: 30,
              message: "生产备案凭证编码长度不能超过30个字符",
            },
          },
          label: { value: "生产备案凭证编码" },
        },
      },
      {
        type: "DatePicker",
        name: "manufacturingFilingStartDate",
        attribute: {
          allowClear: true,
          label: {
            value: "生产备案凭证有效期-开始日期",
          },
          format: "YYYY-MM-DD",
        },
      },
      {
        type: "DatePicker",
        name: "manufacturingFilingEndDate",
        attribute: {
          allowClear: true,
          label: {
            value: "生产备案凭证有效期-截止日期",
          },
          format: "YYYY-MM-DD",
        },
      },
      {
        type: "Upload",
        name: "manufacturingFilingAttachments",
        attribute: {
          label: { value: "生产备案凭证附件" },
          progress: { strokeWidth: 0.1, showInfo: false },
          showUploadList: true,
          maxCount: 1,
          customRequest: () => {},
          beforeUpload: file => {
            let fileType = file.name.substring(file.name.lastIndexOf(".") + 1);
            console.log("file====", file);
            const doesMatchFileType =
              ["jpg", "jpeg", "png", "pdf", "doc", "docx", "xlsx", "xls"].indexOf(fileType.toLowerCase()) > -1;
            if (!doesMatchFileType) {
              message.warning(
                `请上传 ${["jpg", "jpeg", "png", "pdf", "doc", "docx", "xlsx", "xls"].toString()} 格式的文件`,
                4,
              );
              return Upload.LIST_IGNORE;
            }
            const isLimitedSize = file.size / 1024 / 1024 < 50;
            if (!isLimitedSize) {
              message.warning("文件必须小于 50MB!");
              return Upload.LIST_IGNORE;
            }
          },
        },
      },
    ],
  };

  const contactPersonsForm = useMemo(
    () => [
      {
        type: "input",
        name: "name",
        attribute: {
          placeholder: "请填写联系人姓名",
          rule: {
            required: "联系人姓名不能为空",
            maxLength: {
              value: 20,
              message: "联系人姓名长度不能超过20个字符",
            },
          },
          label: { value: "联系人姓名", required: true },
        },
      },
      {
        type: "input",
        name: "tel",
        attribute: {
          placeholder: "请填写联系人联系方式",
          rule: {
            required: "联系人联系方式不能为空",
            pattern: {
              value: /^(0|86|17951)?(1[0-9])[0-9]{9}$/,
              message: "请正确填写手机号",
            },
          },
          label: { value: "法人联系方式", required: true },
        },
      },
      {
        type: "input",
        name: "city",
        attribute: {
          placeholder: "请填写所在城市",
          rule: {
            required: "城市不能为空",
            maxLength: {
              value: 50,
              message: "城市长度不能超过50个字符",
            },
          },
          label: { value: "所在城市", required: true },
        },
      },
      {
        type: "checkbox",
        name: "venderType",
        attribute: {
          rule: {
            required: "请选择供应商类型",
          },
          label: { value: "供应商类型", required: true },
          options: [
            {
              label: "维修商",
              value: "maintainerT",
              disabled: !companyData?.maintainerT,
            },
            {
              label: "制造商",
              value: "manufacturerT",
              disabled: !companyData?.manufacturerT,
            },
            {
              label: "供应商",
              value: "supplierT",
              disabled: !companyData?.supplierT,
            },
          ],
        },
      },
    ],
    [companyData],
  );

  const editCertification = (data, contactPersonData = null) => {
    console.log("editCertification", data, contactPersonData);
    if (data.type == "contact-persons") {
      setFormOption(contactPersonsForm);
    } else {
      setFormOption(formOptionObj[data.type]);
    }
    setEditState(true);
    if (contactPersonData) {
      let venderType = getKeysWithVenderType(contactPersonData);
      contactPersonData.venderType = venderType;
    }

    setEditType({ ...data, contactPersonData }); // 将联系人数据保存到editType中
  };

  const contactPersonsColumns = useMemo(
    () => [
      {
        title: "类型",
        dataIndex: "type",
        key: "type",
      },
      {
        title: "供应商联系人姓名",
        dataIndex: "name",
        key: "name",
      },
      {
        title: "联系方式",
        dataIndex: "tel",
        key: "tel",
      },
      {
        title: "城市",
        dataIndex: "city",
        key: "city",
      },
      {
        title: "操作",
        dataIndex: "tel",
        render: (_, record) => {
          return (
            <>
              <Button
                type="link"
                onClick={() => {
                  editCertification(
                    {
                      type: "contact-persons",
                      title: "编辑供应商联系人",
                    },
                    record,
                  ); // 传递当前联系人数据
                }}
              >
                修改
              </Button>
              <Button
                type="link"
                onClick={() => {
                  console.log(record);
                  deleteCompanydata(record);
                }}
              >
                删除
              </Button>
            </>
          );
        },
      },
    ],
    [editCertification],
  );

  const getKeysWithVenderType = obj => {
    const trueKeys = [];
    for (const key in obj) {
      if (obj[key] === true) {
        trueKeys.push(key);
      }
    }
    return trueKeys;
  };

  // 将data格式转换为compdata格式的方法
  const transformDataToCompData = data => {
    // 创建类型映射
    const typeMapping = {
      maintainerT: "maintainerT",
      manufacturerT: "manufacturerT",
      supplierT: "supplierT",
    };
    // 创建类型标签映射
    const typeLabelMapping = {
      maintainerT: "维修商",
      manufacturerT: "制造商",
      supplierT: "供应商",
    };
    // 初始化转换后的数据
    const compdata = {
      ...data,
      name: data.name || "",
      tel: data.tel || "",
      city: data.city || "",
      supplierT: false,
      maintainerT: false,
      manufacturerT: false,
      type: "",
    };
    // 处理venderType数组，设置对应的布尔值
    if (data.venderType && Array.isArray(data.venderType)) {
      data.venderType.forEach(type => {
        const mappedType = typeMapping[type];
        if (mappedType) {
          compdata[mappedType] = true;
        }
      });
      // 生成type字符串
      const typeLabels = data.venderType.map(type => typeLabelMapping[type]).filter(Boolean);
      compdata.type = typeLabels.join("/");
    }
    return compdata;
  };

  //删除附件处理
  const getDeletedAttachments = async (oldList, newList) => {
    if (!oldList || !Array.isArray(oldList)) return [];
    const newIds = (newList || []).map(item => item.objectId);
    // oldList 中 objectId 不在 newIds 里的就是要删除的
    console.log(
      "oldList.filter(item => item.objectId && !newIds.includes(item.objectId))",
      oldList.filter(item => item.objectId && !newIds.includes(item.objectId)),
    );
    return;
  };

  // 上传附件服务器，并上传供应商信息接口
  const updateFiles = async filesArray => {
    if (!filesArray) return [];
    // 过滤本地需要上传服务器的文件
    let filterArray = filesArray.reduce((result, currentValue) => {
      if (currentValue.size) {
        result.push(currentValue);
      }
      return result;
    }, []);
    return Promise.all(
      filterArray.map(item => {
        return new Promise((resolve, reject) => {
          rest
            .file(item)
            .then(data => {
              const attachment = {
                objectStorageId: data.objectId,
                thumbnail: `/gateway/zuul/hcapmobjecthubservice/api/apm/objectHub/objects/${data.objectId}`,
                original: `/gateway/zuul/hcapmobjecthubservice/api/apm/objectHub/objects/${data.objectId}`,
                ...data,
              };
              resolve(attachment);
            })
            .catch(() => {
              reject("上传失败");
            });
        });
      }),
    );
  };

  const savecompanydata = async data => {
    let companyres;
    setEditState(false);
    setUserInfo({ webLoading: true });
    const updateAttachments = async attachments => {
      const updatedAttachments = await updateFiles(attachments);
      return updatedAttachments.length > 0 ? updatedAttachments : attachments;
    };

    switch (editType.type) {
      case "baseInfoForm":
        data["maintainerT"] = false;
        data["manufacturerT"] = false;
        data["supplierT"] = false;
        data.venderType.forEach(item => {
          data[item] = true;
        });
        break;
      case "licenseForm":
        await getDeletedAttachments(companyData.licenseAttachments, data.licenseAttachments);
        data.licenseAttachments = await updateAttachments(data.licenseAttachments);
        break;
      case "mQualificationForm":
        await getDeletedAttachments(
          companyData.maintenanceQualificationAttachments,
          data.maintenanceQualificationAttachments,
        );
        data.maintenanceQualificationAttachments = await updateAttachments(data.maintenanceQualificationAttachments);
        break;
      case "permitForm":
        data.permitAttachments = await updateAttachments(data.permitAttachments);
        data.afterSaleServiceAttachments = await updateAttachments(data.afterSaleServiceAttachments);
        data.authorizationAttachments = await updateAttachments(data.authorizationAttachments);
        await getDeletedAttachments(companyData.permitAttachments, data.permitAttachments);
        await getDeletedAttachments(companyData.afterSaleServiceAttachments, data.afterSaleServiceAttachments);
        await getDeletedAttachments(companyData.authorizationAttachments, data.authorizationAttachments);
        break;
      case "businessFilingForm":
        data.businessFilingAttachments = await updateAttachments(data.businessFilingAttachments);
        data.businessFilingAfterSaleServiceAttachments = await updateAttachments(
          data.businessFilingAfterSaleServiceAttachments,
        );
        data.businessFilingAuthorizationAttachments = await updateAttachments(
          data.businessFilingAuthorizationAttachments,
        );

        await getDeletedAttachments(companyData.businessFilingAttachments, data.businessFilingAttachments);
        await getDeletedAttachments(
          companyData.businessFilingAfterSaleServiceAttachments,
          data.businessFilingAfterSaleServiceAttachments,
        );
        await getDeletedAttachments(
          companyData.businessFilingAuthorizationAttachments,
          data.businessFilingAuthorizationAttachments,
        );
        break;
      case "manufacturingForm":
        data.manufacturingPermitAttachments = await updateAttachments(data.manufacturingPermitAttachments);

        await getDeletedAttachments(companyData.manufacturingPermitAttachments, data.manufacturingPermitAttachments);
        break;
      case "manufacturingFilingForm":
        data.manufacturingFilingAttachments = await updateAttachments(data.manufacturingFilingAttachments);
        await getDeletedAttachments(companyData.manufacturingFilingAttachments, data.manufacturingFilingAttachments);
        break;
      case "contact-persons":
        console.log("contact-persons-data", data);
        break;
      default:
        break;
    }

    if (editType.type == "contact-persons") {
      // 使用转换方法将data转换为compdata格式
      const transformData = transformDataToCompData(data);
      companyres = await updateContactPersons({ ...transformData, venderCompanyId: model.venderCompanyId });
    } else {
      companyres = await updateVenderCompany(data);
    }

    let copyModel = model;
    copyModel.venderCompany = companyres;
    setCompanyData(companyres);
    setUserInfo(copyModel);
    setUserInfo({ webLoading: false });
  };

  const deleteCompanydata = async data => {
    setUserInfo({ webLoading: true });

    const res = await deleteContactPersons(data);

    let copyModel = model;
    copyModel.venderCompany = res;
    setCompanyData(copyModel);
    setUserInfo(copyModel);
    setUserInfo({ webLoading: false });
  };

  /**DOWNLOAD */
  const handleDOWNLOAD = id => {
    const url = `gateway/zuul/hcapmobjecthubservice/api/apm/objectHub/objects/${id}`;
    const win = window.location.origin;
    const urls = `${win}/${url}`;
    window.open(urls);
    return;
  };

  useEffect(() => {
    let venderType = getKeysWithVenderType(model.venderCompany);
    let companyData = model.venderCompany;
    if (companyData) {
      companyData["venderType"] = venderType;
      setCompanyData(companyData);
    }
  }, [model.venderCompany]);
  return (
    <div className="certification">
      <h3 className="company-name">{companyData?.name}公司</h3>
      {companyData && (
        <Descriptions
          title="基本信息"
          column={{ xs: 1, sm: 2, lg: 3, xl: 4 }}
          extra={
            model.isAdmin && (
              <Button
                type="link"
                onClick={() => {
                  editCertification({
                    type: "baseInfoForm",
                    title: "编辑基本信息",
                  });
                }}
              >
                <EditOutlined />
              </Button>
            )
          }
        >
          <Descriptions.Item label="法人姓名">{companyData.corporate}</Descriptions.Item>
          <Descriptions.Item label="法人联系方式">{companyData.corporateTel}</Descriptions.Item>
          <Descriptions.Item label="统一社会信用代码">{companyData.creditCode}</Descriptions.Item>
          <Descriptions.Item label="所在城市">{companyData.city}</Descriptions.Item>
          <Descriptions.Item label="地址">{companyData.address}</Descriptions.Item>
          <Descriptions.Item label="项目对接人姓名">{companyData.projectDockerName}</Descriptions.Item>
          <Descriptions.Item label="项目对接人联系方式">{companyData.projectDockerTel}</Descriptions.Item>
          <Descriptions.Item label="项目对接人职位">{companyData.projectDockerPosition}</Descriptions.Item>
          <Descriptions.Item label="供应商类型">
            <div>
              <Checkbox checked={companyData.maintainerT} disabled>
                维修商
              </Checkbox>
              <Checkbox checked={companyData.manufacturerT} disabled>
                制造商
              </Checkbox>
              <Checkbox checked={companyData.supplierT} disabled>
                供应商
              </Checkbox>
            </div>
          </Descriptions.Item>
        </Descriptions>
      )}

      <div className="contact-persons">
        <Table
          rowKey={record => {
            return record.id;
          }}
          bordered
          title={() => {
            return (
              <>
                <span>供应商联系人</span>
                <Button
                  type="link"
                  onClick={() => {
                    console.log("add");
                    editCertification({
                      type: "contact-persons",
                      title: "添加供应商联系人",
                    });
                  }}
                >
                  添加供应商联系人
                </Button>
              </>
            );
          }}
          pageSize={5}
          total={2}
          columns={contactPersonsColumns}
          dataSource={companyData?.contactPersons}
        ></Table>
      </div>

      {/* 维修信息 */}
      {companyData?.maintainerT && (
        <Descriptions
          title="维修资质信息"
          extra={
            model.isAdmin && (
              <Button
                type="link"
                onClick={() => {
                  editCertification({
                    type: "mQualificationForm",
                    title: "编辑维系资质信息",
                  });
                }}
              >
                <EditOutlined />
              </Button>
            )
          }
        >
          <Descriptions.Item label="维修资质相关的附件">
            {companyData.maintenanceQualificationAttachments &&
              companyData.maintenanceQualificationAttachments.map(item => {
                return (
                  <span
                    className="download-link"
                    onClick={() => {
                      item.objectId && handleDOWNLOAD(item.objectId);
                      console.log("item", item);
                    }}
                  >
                    <FileDoneOutlined style={{ color: "#1890ff" }}></FileDoneOutlined>
                    {item.objectName}
                  </span>
                );
              })}
          </Descriptions.Item>
        </Descriptions>
      )}

      {companyData?.supplierT && (
        <Descriptions
          className="tender-item"
          title="医疗器械经营许可证信息"
          column={{ xs: 1, sm: 1, xl: 2 }}
          extra={
            model.isAdmin && (
              <Button
                type="link"
                onClick={() => {
                  editCertification({
                    type: "permitForm",
                    title: "编辑许可证信息",
                  });
                }}
              >
                <EditOutlined />
              </Button>
            )
          }
        >
          <Descriptions.Item label="许可证编码">{companyData.permitCode}</Descriptions.Item>
          <Descriptions.Item label="许可证有效期 · 开始日期">{companyData.permitStartDate}</Descriptions.Item>
          <Descriptions.Item label="截止日期">{companyData.permitEndDate}</Descriptions.Item>
          <Descriptions.Item label="许可证附件" span={4}>
            {companyData.permitAttachments &&
              companyData.permitAttachments.map(item => {
                return (
                  <span
                    className="download-link"
                    onClick={() => {
                      item.objectId && handleDOWNLOAD(item.objectId);
                      console.log("item", item);
                    }}
                  >
                    {item.objectName}
                  </span>
                );
              })}
          </Descriptions.Item>
          <Descriptions.Item label="售后服务承诺附件" span={4}>
            {companyData.afterSaleServiceAttachments &&
              companyData.afterSaleServiceAttachments.map(item => {
                return (
                  <span
                    className="download-link"
                    onClick={() => {
                      item.objectId && handleDOWNLOAD(item.objectId);
                      console.log("item", item);
                    }}
                  >
                    <FileDoneOutlined style={{ color: "#1890ff" }}></FileDoneOutlined>
                    {item.objectName}
                  </span>
                );
              })}
          </Descriptions.Item>
          <Descriptions.Item label="授权书附件" span={4}>
            {companyData.authorizationAttachments &&
              companyData.authorizationAttachments.map(item => {
                return (
                  <span
                    className="download-link"
                    onClick={() => {
                      item.objectId && handleDOWNLOAD(item.objectId);
                      console.log("item", item);
                    }}
                  >
                    <FileDoneOutlined style={{ color: "#1890ff" }}></FileDoneOutlined>
                    {item.objectName}
                  </span>
                );
              })}
          </Descriptions.Item>
        </Descriptions>
      )}
      {/* 备案凭证信息 */}
      {companyData?.supplierT && (
        <Descriptions
          className="tender-item"
          title="医疗器械经营备案凭证信息"
          column={{ xs: 1, sm: 2, lg: 3, xl: 4 }}
          extra={
            model.isAdmin && (
              <Button
                type="link"
                onClick={() => {
                  editCertification({
                    type: "businessFilingForm",
                    title: "编辑备案凭证信息",
                  });
                }}
              >
                <EditOutlined />
              </Button>
            )
          }
        >
          <Descriptions.Item label="备案凭证编码">{companyData.businessFilingCode}</Descriptions.Item>
          <Descriptions.Item label="备案凭证有效期 · 开始日期">{companyData.businessFilingStartDate}</Descriptions.Item>
          <Descriptions.Item label="截止日期">{companyData.businessFilingEndDate}</Descriptions.Item>
          <Descriptions.Item label="备案凭证附件" span={4}>
            {companyData.businessFilingAttachments &&
              companyData.businessFilingAttachments.map(item => {
                return (
                  <span
                    className="download-link"
                    onClick={() => {
                      item.objectId && handleDOWNLOAD(item.objectId);
                      console.log("item", item);
                    }}
                  >
                    {item.objectName}
                  </span>
                );
              })}
          </Descriptions.Item>
          <Descriptions.Item label="售后服务承诺附件" span={4}>
            {companyData.businessFilingAfterSaleServiceAttachments &&
              companyData.businessFilingAfterSaleServiceAttachments.map(item => {
                return (
                  <span
                    className="download-link"
                    onClick={() => {
                      item.objectId && handleDOWNLOAD(item.objectId);
                      console.log("item", item);
                    }}
                  >
                    <FileDoneOutlined style={{ color: "#1890ff" }}></FileDoneOutlined>
                    {item.objectName}
                  </span>
                );
              })}
          </Descriptions.Item>
          <Descriptions.Item label="授权书附件" span={4}>
            {companyData.businessFilingAuthorizationAttachments &&
              companyData.businessFilingAuthorizationAttachments.map(item => {
                return (
                  <span
                    className="download-link"
                    onClick={() => {
                      item.objectId && handleDOWNLOAD(item.objectId);
                      console.log("item", item);
                    }}
                  >
                    <FileDoneOutlined style={{ color: "#1890ff" }}></FileDoneOutlined>
                    {item.objectName}
                  </span>
                );
              })}
          </Descriptions.Item>
        </Descriptions>
      )}

      {companyData?.manufacturerT && (
        <Descriptions
          title="医疗器械生产许可证信息"
          column={{ xs: 1, sm: 2, lg: 3, xl: 4 }}
          extra={
            model.isAdmin && (
              <Button
                type="link"
                onClick={() => {
                  editCertification({
                    type: "manufacturingForm",
                    title: "医疗器械生产许可证信息",
                  });
                }}
              >
                <EditOutlined />
              </Button>
            )
          }
        >
          <Descriptions.Item label="生产许可证编码">{companyData.manufacturingPermitCode}</Descriptions.Item>
          <Descriptions.Item label="生产许可证有效期 · 开始日期">
            {companyData.manufacturingPermitStartDate}
          </Descriptions.Item>
          <Descriptions.Item label="截止日期">{companyData.manufacturingPermitEndDate}</Descriptions.Item>
          <Descriptions.Item label="生产许可证附件" span={4}>
            {companyData.manufacturingPermitAttachments &&
              companyData.manufacturingPermitAttachments.map(item => {
                return (
                  <span
                    className="download-link"
                    onClick={() => {
                      item.objectId && handleDOWNLOAD(item.objectId);
                      console.log("item", item);
                    }}
                  >
                    <FileDoneOutlined style={{ color: "#1890ff" }}></FileDoneOutlined>
                    {item.objectName}
                  </span>
                );
              })}
          </Descriptions.Item>
        </Descriptions>
      )}
      {/* 生产备案凭证信息 */}
      {companyData?.manufacturerT && (
        <Descriptions
          title="医疗器械生产备案凭证信息"
          column={{ xs: 1, sm: 2, lg: 3, xl: 4 }}
          extra={
            model.isAdmin && (
              <Button
                type="link"
                onClick={() => {
                  editCertification({
                    type: "manufacturingFilingForm",
                    title: "编辑医疗器械生产备案凭证信息",
                  });
                }}
              >
                <EditOutlined />
              </Button>
            )
          }
        >
          <Descriptions.Item label="生产备案凭证编码">{companyData.manufacturingFilingCode}</Descriptions.Item>
          <Descriptions.Item label="生产备案凭证有效期 · 开始日期">
            {companyData.manufacturingFilingStartDate}
          </Descriptions.Item>
          <Descriptions.Item label="截止日期">{companyData.manufacturingFilingEndDate}</Descriptions.Item>
          <Descriptions.Item label="生产备案凭证附件" span={4}>
            {companyData.manufacturingFilingAttachments &&
              companyData.manufacturingFilingAttachments.map(item => {
                return (
                  <span
                    className="download-link"
                    onClick={() => {
                      item.objectId && handleDOWNLOAD(item.objectId);
                      console.log("item", item);
                    }}
                  >
                    <FileDoneOutlined style={{ color: "#1890ff" }}></FileDoneOutlined>
                    {item.objectName}
                  </span>
                );
              })}
          </Descriptions.Item>
        </Descriptions>
      )}

      <Modal
        closable={false}
        centered
        title={editType && editType.title}
        open={editState}
        footer={null}
        keyboard={false}
        getContainer={() => document.getElementsByClassName("certification")[0]}
        width="500"
      >
        <MyForm
          key={`${editType?.type}-${editType?.contactPersonData?.id || "new"}`} // 使用key强制重新渲染
          data={
            editType?.type !== "contact-persons"
              ? {
                  ...companyData,
                }
              : editType?.contactPersonData || {} // 如果有联系人数据则使用，否则使用空对象
          }
          options={formOption}
          oncancel={() => {
            setEditState(false);
          }}
          onSubmit={data => {
            savecompanydata(data);
          }}
        ></MyForm>
      </Modal>
    </div>
  );
}

export default Certification;
